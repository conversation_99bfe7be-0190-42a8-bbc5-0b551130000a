import { Text, TouchableOpacity, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Modal from 'react-native-modal';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatSocialTime } from '@/src/utilities/datetime';
import Close from '@/src/assets/svgs/Close';
import { PostMediaI } from '@/src/networks/content/types';
import Carousel from '../Carousel';
import UserAvatar from '../UserAvatar';
import { ImageViewerModalProps } from './types';

const ImageViewer = ({ isVisible, onClose, post, initialIndex = 0 }: ImageViewerModalProps) => {
  const timeAgo = formatSocialTime(post.createdAt);
  const insets = useSafeAreaInsets();
  const hasSingleMedia = post.Media?.length === 1;

  const scale = useSharedValue(1);
  const savedScale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);

  const resetTransform = () => {
    scale.value = withSpring(1);
    savedScale.value = 1;
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    savedTranslateX.value = 0;
    savedTranslateY.value = 0;
  };

  const pinchGesture = Gesture.Pinch()
    .onUpdate((e) => {
      scale.value = savedScale.value * e.scale;
    })
    .onEnd(() => {
      if (scale.value < 1) {
        runOnJS(resetTransform)();
      } else if (scale.value > 3) {
        scale.value = withSpring(3);
        savedScale.value = 3;
      } else {
        savedScale.value = scale.value;
      }
    });

  const panGesture = Gesture.Pan()
    .onUpdate((e) => {
      if (scale.value > 1) {
        translateX.value = savedTranslateX.value + e.translationX;
        translateY.value = savedTranslateY.value + e.translationY;
      }
    })
    .onEnd(() => {
      if (scale.value <= 1) {
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        savedTranslateX.value = 0;
        savedTranslateY.value = 0;
      } else {
        savedTranslateX.value = translateX.value;
        savedTranslateY.value = translateY.value;
      }
    });

  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      if (scale.value > 1) {
        runOnJS(resetTransform)();
      } else {
        scale.value = withSpring(2);
        savedScale.value = 2;
      }
    });

  const composedGesture = Gesture.Simultaneous(pinchGesture, panGesture, doubleTapGesture);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const renderZoomableImage = (item: PostMediaI, index: number) => {
    return (
      <View key={index} className="w-full h-full">
        <View className="w-full justify-center items-center flex-1">
          <GestureDetector gesture={composedGesture}>
            <Animated.View className="w-full h-full justify-center items-center">
              <Animated.Image
                source={{ uri: item.fileUrl }}
                className="w-full h-full"
                resizeMode="contain"
                style={animatedStyle}
              />
            </Animated.View>
          </GestureDetector>
        </View>
        {item.caption && (
          <View className="p-4 w-full">
            <Text className="text-white text-base">{item.caption}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderMediaItem = (item: PostMediaI, index: number) => {
    return renderZoomableImage(item, index);
  };

  const renderZoomableCarouselItem = (media: PostMediaI, index: number) => {
    return (
      <View className="w-full h-full" key={index}>
        <GestureDetector gesture={composedGesture}>
          <Animated.View className="w-full h-full justify-center items-center">
            <Animated.Image
              source={{ uri: media.fileUrl }}
              className="w-full h-full"
              resizeMode="contain"
              style={animatedStyle}
            />
          </Animated.View>
        </GestureDetector>
      </View>
    );
  };

  return (
    <View
      className="absolute pointer-events-none"
      style={{ top: insets.top, bottom: insets.bottom }}
    >
      <Modal
        isVisible={isVisible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        style={{ margin: 0 }}
        animationIn="fadeIn"
        animationOut="fadeOut"
        backdropOpacity={1}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <View className="flex-1 bg-black">
          <View className="h-full w-full" style={{ paddingTop: insets.top }}>
            <View className="flex-row items-center justify-between px-4">
              <View className="flex-row items-center flex-1">
                <UserAvatar
                  avatarUri={post.Profile.avatar}
                  name={post.Profile.name}
                  width={40}
                  height={40}
                  className="mr-3"
                />
                <View className="flex-1 ml-3">
                  <Text className="text-white font-bold text-base">{post.Profile.name}</Text>
                  <Text className="text-gray-300 text-xs" numberOfLines={1}>
                    {post.Profile.designation?.name}{' '}
                    {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
                  </Text>
                  <Text className="text-gray-400 text-xs">{timeAgo}</Text>
                </View>
              </View>
              <TouchableOpacity onPress={onClose} className="p-2 bg-white rounded-full">
                <Close stroke="white" width={2} height={2} />
              </TouchableOpacity>
            </View>
            <View className="flex-1 justify-center items-center">
              {hasSingleMedia ? (
                renderMediaItem(post.Media[0], 0)
              ) : (
                <Carousel
                  showArrows={true}
                  showDots={false}
                  showSlideNumbers={true}
                  activeColor="#FFFFFF"
                  inactiveColor="#FFFFFF50"
                  arrowClassName="bg-white/70 p-3 rounded-full"
                  dotClassName="h-3 w-3"
                  autoPlay={false}
                  initialIndex={initialIndex}
                >
                  {post.Media?.map((media, index) => renderZoomableCarouselItem(media, index))}
                </Carousel>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ImageViewer;
