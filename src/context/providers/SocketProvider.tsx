import { createContext, useContext, useEffect, useRef, useState } from 'react';
import Config from 'react-native-config';
import useStorage from '@/src/hooks/storage';
import {
  MessageHandler,
  SocketContextProviderProps,
  SocketContextValue,
  SocketMessage,
} from './types';

const SocketContext = createContext<SocketContextValue | null>(null);

const getBaseUrl = () => {
  console.log('[SocketContext] getBaseUrl - Config.ENV:', Config.ENV);
  console.log('[SocketContext] getBaseUrl - Config.BASE_URL:', Config.BASE_URL);
  
  if (Config.ENV === 'development') {
    console.log('[SocketContext] getBaseUrl - Using development URL');
    return 'localhost:4003/ws/chat';
  }
  const cleanBaseUrl = Config.BASE_URL?.replace(/^https?:\/\//, '') || '';
  console.log('[SocketContext] getBaseUrl - Clean base URL:', cleanBaseUrl);
  const finalUrl = `${cleanBaseUrl}/ws/chat`;
  console.log('[SocketContext] getBaseUrl - Final URL:', finalUrl);
  return finalUrl;
};

const baseUrl = getBaseUrl();
console.log('[SocketContext] Base URL initialized:', baseUrl);

const SocketContextProvider = ({ children }: SocketContextProviderProps) => {
  console.log('[SocketContext] Provider initializing');
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [profileId, setProfileId] = useState<string | null>(null);
  const { getStorage } = useStorage();

  const socketRef = useRef<WebSocket | null>(null);
  const messageHandlersRef = useRef<MessageHandler>({});
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const connectionPromiseRef = useRef<Promise<void> | null>(null);

  useEffect(() => {
    console.log('[SocketContext] Loading profileId from storage');
    const loadProfileId = async () => {
      try {
        const storedProfileId = await getStorage('userProfileId');
        console.log('[SocketContext] Retrieved profileId from storage:', storedProfileId);
        setProfileId(storedProfileId ?? null);
      } catch (error) {
        console.error('[SocketContext] Error loading profileId:', error);
      }
    };
    loadProfileId();
  }, [getStorage]);

  const connect = () => {
    console.log('[SocketContext] connect() called');
    console.log('[SocketContext] connect() - profileId:', profileId);
    console.log('[SocketContext] connect() - isConnected:', isConnected);
    console.log('[SocketContext] connect() - isConnecting:', isConnecting);
    console.log('[SocketContext] connect() - socket readyState:', socketRef.current?.readyState);
    
    if (!profileId) {
      console.log('[SocketContext] connect() - No profileId available, rejecting');
      return Promise.reject(new Error('No profileId available'));
    }

    if (isConnected || isConnecting || socketRef.current?.readyState === WebSocket.OPEN) {
      console.log('[SocketContext] connect() - Already connected/connecting, resolving');
      return Promise.resolve();
    }

    if (socketRef.current?.readyState === WebSocket.CONNECTING) {
      console.log('[SocketContext] connect() - Already connecting, returning existing promise');
      return connectionPromiseRef.current || Promise.resolve();
    }

    console.log('[SocketContext] connect() - Starting new connection');
    setIsConnecting(true);
    connectionPromiseRef.current = new Promise<void>((resolve, reject) => {
      try {
        const url = `${Config.ENV === 'production' ? 'wss' : 'ws'}://${baseUrl}/${profileId}`;
        console.log('[SocketContext] connect() - WebSocket URL:', url);
        socketRef.current = new WebSocket(url);

        const connectionTimeout = setTimeout(() => {
          console.log('[SocketContext] connect() - Connection timeout reached');
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (socketRef.current?.readyState === WebSocket.CONNECTING) {
            console.log('[SocketContext] connect() - Closing socket due to timeout');
            socketRef.current.close();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        socketRef.current.onopen = () => {
          console.log('[SocketContext] WebSocket onopen - Connection established');
          clearTimeout(connectionTimeout);
          setIsConnected(true);
          setIsConnecting(false);
          reconnectAttemptsRef.current = 0;
          connectionPromiseRef.current = null;
          console.log('[SocketContext] WebSocket onopen - State updated, resolving promise');
          resolve();
        };

        socketRef.current.onclose = () => {
          console.log('[SocketContext] WebSocket onclose - Connection closed');
          clearTimeout(connectionTimeout);
          setIsConnected(false);
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          console.log('[SocketContext] WebSocket onclose - Attempting reconnect');
          attemptReconnect();
        };

        socketRef.current.onerror = (error) => {
          console.error('[SocketContext] WebSocket onerror:', error);
          clearTimeout(connectionTimeout);
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (!isConnected) {
            console.log('[SocketContext] WebSocket onerror - Not connected, rejecting promise');
            reject(error);
          }
        };

        socketRef.current.onmessage = (event) => {
          console.log('[SocketContext] WebSocket onmessage - Raw data:', event.data);
          try {
            const message: SocketMessage = JSON.parse(event.data);
            console.log('[SocketContext] WebSocket onmessage - Parsed message:', message);
            const { type, data } = message;
            if (type === 'connection-established') {
              console.log('[SocketContext] WebSocket onmessage - Connection established message, ignoring');
              return;
            }
            if (type && messageHandlersRef.current[type]) {
              console.log('[SocketContext] WebSocket onmessage - Calling handler for type:', type);
              messageHandlersRef.current[type](data);
            } else {
              console.log('[SocketContext] WebSocket onmessage - No handler found for type:', type);
            }
          } catch (error) {
            console.error('[SocketContext] WebSocket onmessage - Error processing message:', error);
          }
        };
      } catch (error) {
        console.error('[SocketContext] connect() - Error creating WebSocket:', error);
        setIsConnecting(false);
        connectionPromiseRef.current = null;
        reject(error);
      }
    });

    return connectionPromiseRef.current;
  };

  const attemptReconnect = () => {
    console.log('[SocketContext] attemptReconnect() called');
    console.log('[SocketContext] attemptReconnect() - Current attempts:', reconnectAttemptsRef.current);
    console.log('[SocketContext] attemptReconnect() - isConnected:', isConnected);
    console.log('[SocketContext] attemptReconnect() - isConnecting:', isConnecting);
    
    if (reconnectAttemptsRef.current >= 5) {
      console.log('[SocketContext] attemptReconnect() - Max attempts reached, stopping');
      return;
    }
    if (isConnected || isConnecting) {
      console.log('[SocketContext] attemptReconnect() - Already connected/connecting, stopping');
      return;
    }

    if (reconnectTimeoutRef.current) {
      console.log('[SocketContext] attemptReconnect() - Clearing existing timeout');
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(30000, 3000 * Math.pow(1.5, reconnectAttemptsRef.current));
    console.log('[SocketContext] attemptReconnect() - Reconnect delay:', delay);
    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('[SocketContext] attemptReconnect() - Timeout fired, attempting reconnect');
      reconnectAttemptsRef.current++;
      console.log('[SocketContext] attemptReconnect() - Incremented attempts to:', reconnectAttemptsRef.current);
      connect().catch((error) => {
        console.error('[SocketContext] attemptReconnect() - Reconnect failed:', error);
      });
    }, delay);
  };

  const disconnect = (permanent = false) => {
    console.log('[SocketContext] disconnect() called, permanent:', permanent);
    console.log('[SocketContext] disconnect() - Current socket state:', socketRef.current?.readyState);
    
    if (socketRef.current) {
      console.log('[SocketContext] disconnect() - Closing WebSocket');
      socketRef.current.close(1000);
      socketRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      console.log('[SocketContext] disconnect() - Clearing reconnect timeout');
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    connectionPromiseRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    console.log('[SocketContext] disconnect() - State reset');
  };

  const sendMessage = (type: string, data: any) => {
    console.log('[SocketContext] sendMessage() called');
    console.log('[SocketContext] sendMessage() - type:', type);
    console.log('[SocketContext] sendMessage() - data:', data);
    console.log('[SocketContext] sendMessage() - isConnected:', isConnected);
    console.log('[SocketContext] sendMessage() - socket readyState:', socketRef.current?.readyState);
    
    if (!isConnected || !socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.log('[SocketContext] sendMessage() - Socket not connected, rejecting');
      return Promise.reject(new Error('Socket not connected'));
    }

    try {
      const message = JSON.stringify({ type, data });
      console.log('[SocketContext] sendMessage() - Sending message:', message);
      socketRef.current.send(message);
      console.log('[SocketContext] sendMessage() - Message sent successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('[SocketContext] sendMessage() - Error sending message:', error);
      return Promise.reject(error);
    }
  };

  const onMessage = (type: string, handler: (data: any) => void) => {
    console.log('[SocketContext] onMessage() - Registering handler for type:', type);
    messageHandlersRef.current[type] = handler;
    console.log('[SocketContext] onMessage() - Current handlers:', Object.keys(messageHandlersRef.current));
  };

  const removeMessageHandler = (type: string) => {
    console.log('[SocketContext] removeMessageHandler() - Removing handler for type:', type);
    delete messageHandlersRef.current[type];
    console.log('[SocketContext] removeMessageHandler() - Remaining handlers:', Object.keys(messageHandlersRef.current));
  };

  useEffect(() => {
    console.log('[SocketContext] Main useEffect triggered, profileId:', profileId);
    
    if (!profileId) {
      console.log('[SocketContext] Main useEffect - No profileId, skipping connection setup');
      return;
    }

    console.log('[SocketContext] Main useEffect - Setting up heartbeat and connecting');
    const heartbeatInterval = setInterval(() => {
      console.log('[SocketContext] Heartbeat tick');
      console.log('[SocketContext] Heartbeat - isConnected:', isConnected);
      console.log('[SocketContext] Heartbeat - socket readyState:', socketRef.current?.readyState);
      
      if (isConnected && socketRef.current?.readyState === WebSocket.OPEN) {
        console.log('[SocketContext] Heartbeat - Sending ping');
        sendMessage('ping', {}).catch((error) => {
          console.error('[SocketContext] Heartbeat - Ping failed:', error);
          socketRef.current?.close();
        });
      } else {
        console.log('[SocketContext] Heartbeat - Not connected, skipping ping');
      }
    }, 30000);

    console.log('[SocketContext] Main useEffect - Initiating connection');
    connect().catch((error) => {
      console.error('[SocketContext] Main useEffect - Initial connection failed:', error);
    });

    return () => {
      console.log('[SocketContext] Main useEffect cleanup');
      clearInterval(heartbeatInterval);
      disconnect();
    };
  }, [profileId]);

  const value: SocketContextValue = {
    isConnected,
    isConnecting,
    sendMessage,
    onMessage,
    removeMessageHandler,
    disconnect,
  };

  console.log('[SocketContext] Provider rendering with value:', {
    isConnected,
    isConnecting,
    profileId
  });

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>;
};

export const useSocket = () => {
  console.log('[SocketContext] useSocket() called');
  const context = useContext(SocketContext);
  if (!context) {
    console.error('[SocketContext] useSocket() - No context found, throwing error');
    throw new Error('useSocket must be used within a SocketContextProvider');
  }
  console.log('[SocketContext] useSocket() - Returning context');
  return context;
};

export default SocketContextProvider;